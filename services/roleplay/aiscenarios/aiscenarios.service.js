'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const Model = require('./aiscenarios.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const i18next = require('i18next');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'aiscenarios',
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService],

  settings: {
    entityValidator: {
      courseId: {type: 'string'},
      aiPersonaId: {type: 'string'},
      roleplayInstructionId: {type: 'string', optional: true},
      taskIds: {type: 'array', optional: true, items: 'string'},
      name: {type: 'string', min: 2, max: 255},
      description: {type: 'string', optional: true, max: 2000},

      estimatedCallTimeInMinutes: {type: 'number', optional: true, min: 0},
      organizationId: {type: 'string', optional: true},
      aiSpeaksFirst: {type: 'boolean', optional: true},
      initialAiMessage: {type: 'string', optional: true, max: 1000},
    },
    populates: {
      courseId: 'courses.get',
      aiPersonaId: 'aipersonas.get',
      roleplayInstructionId: 'roleplayinstruction.get',
      taskIds: 'tasks.get',
      organizationId: 'organizations.get',
      createdBy: 'users.get',
      updatedBy: 'users.get',
    },
    populateOptions: ['courseId', 'aiPersonaId', 'taskIds', 'createdBy', 'updatedBy'],
    fields: [
      '_id',
      'courseId',
      'aiPersonaId',
      'roleplayInstructionId',
      'taskIds',
      'name',
      'description',
      'simulationType',
      'passScore',
      'estimatedCallTimeInMinutes',
      'organizationId',
      'createdBy',
      'updatedBy',
      'createdAt',
      'updatedAt',
      'isDeleted',
      'status',
      'aiSpeaksFirst',
      'initialAiMessage',
      'isCompleted', // Thông tin user đã hoàn thành scenario này chưa
    ],
    defaultSort: 'createdAt',
  },

  hooks: {
    after: {
      create: async (ctx, scenario) => {
        ctx.emit('aiscenarios.created', {scenario});
        return scenario;
      },
      update: async (ctx, scenario) => {
        ctx.emit('aiscenarios.updated', {scenario});
        return scenario;
      },
      remove: async (ctx, scenario) => {
        ctx.emit('aiscenarios.deleted', {scenario});
        return scenario;
      },
    },
    before: {},
  },

  dependencies: ['courses', 'aipersonas', 'roleplayinstruction', 'tasks', 'organizations', 'users'],

  events: {
    // Xử lý khi task bị xóa
    'tasks.deleted': {
      async handler(payload) {
        if (payload.task && payload.task._id) {
          // Tìm tất cả scenarios có chứa taskId này và xóa nó khỏi mảng taskIds
          const taskId = payload.task._id;

          try {
            // Tìm tất cả scenarios có chứa taskId này
            const scenarios = await this.adapter.find({
              query: {
                taskIds: {$in: [taskId]},
                isDeleted: {$ne: true},
              },
            });

            // Cập nhật từng scenario để xóa taskId
            for (const scenario of scenarios) {
              const updatedTaskIds = scenario.taskIds.filter(id => id.toString() !== taskId.toString());
              await this.adapter.updateById(scenario._id, {
                $set: {taskIds: updatedTaskIds},
              });
              this.logger.info(`Removed task ${taskId} from scenario ${scenario._id}`);
            }
          } catch (error) {
            this.logger.error(`Error removing task ${taskId} from scenarios:`, error);
          }
        }
      },
    },
  },

  actions: {
    // Tạo AI Scenario mới
    create: {
      rest: 'POST /',
      params: {
        courseId: {type: 'string'},
        aiPersonaId: {type: 'string'},
        roleplayInstructionId: {type: 'string', optional: true},
        taskIds: {type: 'array', optional: true, items: 'string'},
        name: {type: 'string', min: 2, max: 255},
        description: {type: 'string', optional: true, max: 2000},

        estimatedCallTimeInMinutes: {type: 'number', optional: true, min: 0},
        organizationId: {type: 'string', optional: true},
        aiSpeaksFirst: {type: 'boolean', optional: true},
        initialAiMessage: {type: 'string', optional: true, max: 1000},
      },
      async handler(ctx) {
        const {
          courseId,
          aiPersonaId,
          roleplayInstructionId,
          taskIds,
          name,
          description,

          estimatedCallTimeInMinutes,
          organizationId,
          aiSpeaksFirst,
          initialAiMessage,
        } = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Validation cho aiSpeaksFirst và initialAiMessage
        if (aiSpeaksFirst === true && (!initialAiMessage || initialAiMessage.trim() === '')) {
          throw new MoleculerClientError(
            i18next.t('error.initial_ai_message_required', 'Tin nhắn khởi tạo AI là bắt buộc khi AI nói trước'),
            400,
          );
        }

        // Kiểm tra course tồn tại
        const course = await ctx.call('courses.get', {id: courseId}).catch(() => null);
        if (!course) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Khóa học không tồn tại'), 404);
        }

        // Kiểm tra AI Persona tồn tại
        const persona = await ctx.call('aipersonas.get', {id: aiPersonaId}).catch(() => null);
        if (!persona) {
          throw new MoleculerClientError(i18next.t('error.persona_not_found', 'AI Persona không tồn tại'), 404);
        }

        // Kiểm tra tasks nếu được chỉ định
        if (taskIds && taskIds.length > 0) {
          for (const taskId of taskIds) {
            const task = await ctx.call('tasks.get', {id: taskId}).catch(() => null);
            if (!task) {
              throw new MoleculerClientError(i18next.t('error.task_not_found', 'Task không tồn tại'), 404);
            }
            // Kiểm tra task thuộc về cùng organization
            if (
              task.organizationId &&
              task.organizationId.toString() !== (organizationId || user.organizationId).toString()
            ) {
              throw new MoleculerClientError(
                i18next.t('error.task_not_belong_organization', 'Task không thuộc về tổ chức này'),
                400,
              );
            }
          }
        }

        const scenarioData = {
          courseId,
          aiPersonaId,
          roleplayInstructionId,
          taskIds: taskIds || [],
          name,
          description,

          estimatedCallTimeInMinutes,
          organizationId: organizationId || user.organizationId,
          aiSpeaksFirst: aiSpeaksFirst || false,
          initialAiMessage: initialAiMessage || '',
          createdBy: user._id,
          updatedBy: user._id,
          status: 'draft',
        };

        const scenario = await this.adapter.insert(scenarioData);
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, scenario);
      },
    },

    // Lấy tất cả scenarios của một course
    getByCourse: {
      rest: 'GET /course/:courseId',
      params: {
        courseId: {type: 'string'},
        studentId: {type: 'string', optional: true}, // Optional để lấy thông tin completion của user cụ thể
      },
      async handler(ctx) {
        const {courseId, studentId} = ctx.params;
        const user = ctx.meta.user;

        const scenarios = await this.adapter.find({
          query: {courseId, isDeleted: false},
          sort: 'createdAt',
        });

        const transformedScenarios = await this.transformDocuments(
          ctx,
          {populate: this.settings.populateOptions},
          scenarios,
        );

        // Nếu có studentId hoặc user, thêm thông tin completion cho từng scenario
        const targetStudentId = studentId || user?._id;
        if (targetStudentId && Array.isArray(transformedScenarios)) {
          const enrichedScenarios = await Promise.all(
            transformedScenarios.map(async scenario => {
              try {
                const isCompleted = await this.checkScenarioCompletion(ctx, scenario._id, targetStudentId);
                return {
                  ...scenario,
                  isCompleted,
                };
              } catch (error) {
                this.logger.error(`Error checking completion for scenario ${scenario._id}:`, error);
                return {
                  ...scenario,
                  isCompleted: false,
                };
              }
            }),
          );
          return enrichedScenarios;
        }

        return transformedScenarios;
      },
    },

    // Lấy scenario với đầy đủ thông tin liên quan (persona, tasks, roleplay instruction)
    getScenarioWithDetails: {
      rest: 'GET /:id/details',
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        const {id} = ctx.params;

        const scenario = await this.adapter.findById(id);
        if (!scenario) {
          throw new MoleculerClientError(i18next.t('error.scenario_not_found', 'Kịch bản không tồn tại'), 404);
        }

        // Transform với populate đầy đủ
        const populateOptions = ['courseId', 'aiPersonaId', 'roleplayInstructionId', 'taskIds'];

        return this.transformDocuments(ctx, {populate: populateOptions}, scenario);
      },
    },

    // Cập nhật AI Scenario
    update: {
      rest: 'PUT /:id',
      params: {
        id: {type: 'string'},
        aiPersonaId: {type: 'string', optional: true},
        roleplayInstructionId: {type: 'string', optional: true},
        taskIds: {type: 'array', optional: true, items: 'string'},
        name: {type: 'string', optional: true, min: 2, max: 255},
        description: {type: 'string', optional: true, max: 2000},

        estimatedCallTimeInMinutes: {type: 'number', optional: true, min: 0},
        status: {type: 'string', optional: true, enum: ['draft', 'published', 'archived']},
        aiSpeaksFirst: {type: 'boolean', optional: true},
        initialAiMessage: {type: 'string', optional: true, max: 1000},
      },
      async handler(ctx) {
        const {id, ...updateData} = ctx.params;
        const user = ctx.meta.user;

        const scenario = await this.adapter.findById(id);
        if (!scenario) {
          throw new MoleculerClientError(i18next.t('error.scenario_not_found', 'Kịch bản không tồn tại'), 404);
        }

        // Kiểm tra AI Persona tồn tại nếu được cập nhật
        if (updateData.aiPersonaId) {
          const persona = await ctx.call('aipersonas.get', {id: updateData.aiPersonaId}).catch(() => null);
          if (!persona) {
            throw new MoleculerClientError(i18next.t('error.persona_not_found', 'AI Persona không tồn tại'), 404);
          }
        }

        // Kiểm tra tasks nếu được cập nhật
        if (updateData.taskIds && updateData.taskIds.length > 0) {
          for (const taskId of updateData.taskIds) {
            const task = await ctx.call('tasks.get', {id: taskId}).catch(() => null);
            if (!task) {
              throw new MoleculerClientError(i18next.t('error.task_not_found', 'Task không tồn tại'), 404);
            }
            // Kiểm tra task thuộc về cùng organization
            if (task.organizationId && task.organizationId.toString() !== scenario.organizationId.toString()) {
              throw new MoleculerClientError(
                i18next.t('error.task_not_belong_organization', 'Task không thuộc về tổ chức này'),
                400,
              );
            }
          }
        }

        // Validation cho aiSpeaksFirst và initialAiMessage
        if (updateData.aiSpeaksFirst === true && (!updateData.initialAiMessage || updateData.initialAiMessage.trim() === '')) {
          throw new MoleculerClientError(
            i18next.t('error.initial_ai_message_required', 'Tin nhắn khởi tạo AI là bắt buộc khi AI nói trước'),
            400,
          );
        }

        updateData.updatedBy = user._id;
        const updated = await this.adapter.updateById(id, {$set: updateData});

        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, updated);
      },
    },

    // Thêm tasks vào scenario
    addTasksToScenario: {
      rest: 'POST /:id/tasks',
      params: {
        id: {type: 'string'},
        taskIds: {type: 'array', items: 'string'},
      },
      async handler(ctx) {
        const {id, taskIds} = ctx.params;
        const user = ctx.meta.user;

        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy scenario hiện tại
        const scenario = await this.adapter.findById(id);
        if (!scenario || scenario.isDeleted) {
          throw new MoleculerClientError(i18next.t('error.scenario_not_found', 'Không tìm thấy kịch bản'), 404);
        }

        // Kiểm tra tasks tồn tại
        for (const taskId of taskIds) {
          const task = await ctx.call('tasks.get', {id: taskId}).catch(() => null);
          if (!task) {
            throw new MoleculerClientError(i18next.t('error.task_not_found', 'Task không tồn tại'), 404);
          }
          // Kiểm tra task thuộc về cùng organization
          if (task.organizationId && task.organizationId.toString() !== scenario.organizationId.toString()) {
            throw new MoleculerClientError(
              i18next.t('error.task_not_belong_organization', 'Task không thuộc về tổ chức này'),
              400,
            );
          }
        }

        // Thêm taskIds vào scenario (tránh trùng lặp)
        const currentTaskIds = scenario.taskIds || [];
        const newTaskIds = [...new Set([...currentTaskIds.map(id => id.toString()), ...taskIds])];

        const updated = await this.adapter.updateById(id, {
          $set: {
            taskIds: newTaskIds,
            updatedBy: user._id,
          },
        });

        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, updated);
      },
    },

    // Xóa AI Scenario (xóa mềm)
    remove: {
      rest: 'DELETE /:id',
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const user = ctx.meta.user;

        const scenario = await this.adapter.findById(id);
        if (!scenario) {
          throw new MoleculerClientError(i18next.t('error.scenario_not_found', 'Kịch bản không tồn tại'), 404);
        }

        const updated = await this.adapter.updateById(id, {
          $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedBy: user._id,
          },
        });

        return this.transformDocuments(ctx, {}, updated);
      },
    },
  },

  methods: {
    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: 'name,description',
        query: JSON.stringify(query),
        fields: this.settings.fields.join(' '),
        sort,
        populate: this.settings.populateOptions,
      };
    },

    /**
     * Kiểm tra xem user đã hoàn thành scenario này chưa
     * Logic mới: Scenario được coi là hoàn thành khi có điểm số >= 70
     *
     * @param {Object} ctx - Moleculer context
     * @param {String} scenarioId - ID của AI scenario
     * @param {String} studentId - ID của học viên
     * @returns {Boolean} - true nếu đã hoàn thành, false nếu chưa
     */
    async checkScenarioCompletion(ctx, scenarioId, studentId) {
      try {
        // Tìm sessions của user cho scenario này với điều kiện đã hoàn thành và có analysis
        const sessions = await ctx.call('roleplaysessions.find', {
          query: {
            studentId: studentId,
            aiScenarioId: scenarioId,
            status: {$in: ['completed', 'analyzed']},
            isDeleted: false,
            analysisId: {$exists: true},
          },
          populate: ['analysisId'],
          limit: 10, // Lấy nhiều hơn để tìm session có điểm >= 70
        });

        if (!sessions || sessions.length === 0) {
          return false;
        }

        // Kiểm tra xem có session nào đạt điểm >= 70 không
        const hasCompletedSession = sessions.some(session => {
          const score = session.analysisId?.result?.simulationScore;
          return score !== undefined && score >= 70;
        });

        return hasCompletedSession;
      } catch (error) {
        this.logger.error(
          `Error checking scenario completion for scenario ${scenarioId}, student ${studentId}:`,
          error,
        );
        return false;
      }
    },
  },

  created() {},

  async started() {
    this.logger.info('AI Scenarios service started');
  },

  async stopped() {
    this.logger.info('AI Scenarios service stopped');
  },
};
