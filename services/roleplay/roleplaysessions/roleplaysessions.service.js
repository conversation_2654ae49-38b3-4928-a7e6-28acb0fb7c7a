'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const Model = require('./roleplaysessions.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const axios = require('axios');
const path = require('path');
const fs = require('fs');
const storageDir = path.join(__dirname, 'storage');
const sdk = require('microsoft-cognitiveservices-speech-sdk');
const i18next = require('i18next');
const {INPUT_TYPE} = require('../../../constants/constant');
const {MoleculerClientError} = require('moleculer').Errors;
const ffmpeg = require('fluent-ffmpeg');
const wav = require('wav');
const audioUtils = require('./audioUtils'); // Import file tiện ích mới
// const textUtils = require('./textUtils'); // Bỏ dòng này

// Đường dẫn tới mô hình VAD. Đảm bảo bạn đã tải file này và đặt đúng đường dẫn.
const sileroVadModelPath = path.join(__dirname, 'models', 'silero_vad.onnx');

// Lưu trữ trạng thái kết nối của các client
const connectionState = new Map();

// Cấu trúc trạng thái cho mỗi kết nối
const createConnectionState = () => {
  // Cấu hình cụ thể cho instance VAD này
  const vadOptions = {
    threshold: 0.5, // Giảm ngưỡng VAD để nhạy hơn
    // Các tùy chọn khác có thể thêm vào đây nếu muốn ghi đè VAD_DEFAULTS từ audioUtils
    minSpeechDuration: 0.25,
  };

  let sherpaVadInstance = audioUtils.createSherpaVadInstance(sileroVadModelPath, vadOptions);

  return {
    audioChunks: [],
    allAudioChunksForSession: [],
    allAiAudioChunksForSession: [],
    audioBuffer: Buffer.alloc(0),
    lastVoiceActivity: Date.now(),
    isProcessingSpeech: false,
    conversationHistory: [
      {
        role: 'user',
        content: 'Alo',
      },
    ],
    userId: null,
    sessionId: null,
    isSessionActive: false,
    startTime: Date.now(),
    currentTurn: {
      role: null,
      text: '',
      audioChunks: [],
    },
    clientAudioFormat: {
      sampleRate: 16000,
      channels: 1,
      bitDepth: 16,
    },
    isHandlingChunk: false,
    chunkQueue: [],
    sttStreamId: null,
    isStudentSpeaking: false,
    currentStudentTranscript: '',
    socket: null,
    sherpaVad: sherpaVadInstance, // Instance VAD từ audioUtils
    currentStudentAudioChunks: [],
    currentAiTurnAudioChunks: [],
    isAiResponding: false, // Cờ báo hiệu AI đang trong quá trình phản hồi (LLM hoặc TTS)
    isAiInterruptedByStudent: false, // Cờ báo hiệu AI bị học viên ngắt lời
  };
};

module.exports = {
  name: 'roleplaysessions',
  mixins: [
    DbMongoose(Model),
    FunctionsCommon,
    BaseService,
    FileMixin,
    require('../../../mixins/roleplayAudio.mixin'),
    require('../../../mixins/roleplaySTT.mixin'),
    require('../../../mixins/roleplayLLM.mixin'),
    require('../../../mixins/roleplaySessionUtil.mixin'),
  ], // Bổ sung audio, STT, LLM và sessionUtil mixin

  settings: {
    entityValidator: {},
    populates: {
      createdBy: 'users.get',
      updatedBy: 'users.get',
      studentId: 'users.get', // Học viên của phiên này
      taskId: 'tasks.get', // Nhiệm vụ của phiên nhập vai
      taskIds: 'tasks.get', // Nhiệm vụ của phiên nhập vai
      personaId: 'aipersonas.get', // Persona AI đã sử dụng
      recordingId: 'files.get', // File ghi âm của phiên
      courseId: 'courses.get', // Khóa học mà phiên này thuộc về
      analysisId: 'roleplay.analysises.get', // Thêm populate cho analysisId
      aiScenarioId: 'aiscenarios.get', // AI Scenario được sử dụng cho phiên này
    },
    populateOptions: [
      'studentId',
      'taskId',
      'personaId',
      'recordingId',
      'courseId',
      'createdBy',
      'updatedBy',
      'analysisId',
      'aiScenarioId',
    ], // Thêm 'analysisId' và 'aiScenarioId'
  },

  hooks: {},
  dependencies: [],

  actions: {
    getAllByStudent: {
      rest: ['GET /by-student', 'GET /by-student/:studentIdParam'],
      async handler(ctx) {
        const {
          studentIdParam,
          populate,
          fields,
          page,
          pageSize,
          limit,
          sort,
          search,
          searchFields,
          query,
          courseId,
          taskId,
          status,
        } = ctx.params;

        const user = ctx.meta.user;

        let targetStudentId;

        if (studentIdParam) {
          if (!user || !user.isSystemAdmin) {
            throw new MoleculerClientError(i18next.t('error_permission_denied'), 403);
          }
          targetStudentId = studentIdParam;
        } else {
          // Student fetching their own sessions
          if (!user || !user._id) {
            throw new MoleculerClientError(i18next.t('error_unauthorized'), 401);
          }
          targetStudentId = user._id.toString();
        }

        const listQuery = {
          ...(query ? (typeof query === 'string' ? JSON.parse(query) : query) : {}),
          studentId: targetStudentId,
          isDeleted: false, // Thường thì không muốn lấy session đã xóa mềm
        };

        if (courseId) {
          listQuery.courseId = courseId;
        }
        if (taskId) {
          listQuery.taskId = taskId;
        }
        if (status) {
          listQuery.status = status;
        }

        const listParams = {
          populate: populate || ['taskId', 'personaId', 'analysisId', 'courseId', 'aiScenarioId'],
          fields,
          page,
          limit: +limit || +pageSize || 10,
          sort: sort || '-endTime', // Default sort by endTime descending
          search,
          // searchFields: searchFields || 'taskId.name,personaId.name,courseId.name', // Default search fields
          query: JSON.stringify(listQuery),
        };

        return ctx.call('roleplaysessions.list', listParams);
      },
    },

    // Xử lý bắt đầu phiên roleplay
    startSession: {
      async handler(ctx) {
        try {
          // aiPersonaId được gửi từ client
          let {sessionId, socketId, userId, studentId, courseId, aiPersonaId} = ctx.params;
          this.logger.info(
            `Bắt đầu phiên roleplay: client-sessionId ${sessionId} cho courseId: ${courseId}, aiPersonaId: ${aiPersonaId}`,
          );

          let courseData = null;
          let taskEntities = [];
          let taskIds = [];
          let aiScenario = null;

          // Kiểm tra aiPersonaId được gửi từ client
          if (!aiPersonaId) {
            throw new MoleculerClientError('Thiếu thông tin aiPersonaId', 400);
          }

          if (courseId) {
            try {
              // Lấy thông tin course
              courseData = await this.broker.call('courses.get', {id: courseId});
              if (!courseData) {
                this.logger.warn(`Không tìm thấy courseData cho courseId ${courseId}.`);
              } else {
                // Lấy AI scenario tương ứng với aiPersonaId và courseId
                try {
                  const scenarios = await this.broker.call('aiscenarios.getByCourse', {courseId});
                  console.log('scenarios', scenarios);
                  aiScenario = scenarios.find(scenario => {
                    const scenarioPersonaId =
                      typeof scenario.aiPersonaId === 'object' && scenario.aiPersonaId._id
                        ? scenario.aiPersonaId._id.toString()
                        : scenario.aiPersonaId.toString();
                    return scenarioPersonaId === aiPersonaId.toString();
                  });

                  if (aiScenario) {
                    this.logger.info(`Tìm thấy AI scenario với aiPersonaId: ${aiPersonaId}`);

                    // Lấy tasks từ scenario
                    if (aiScenario.taskIds && aiScenario.taskIds.length > 0) {
                      taskIds = aiScenario.taskIds.map(t => (typeof t === 'object' ? t._id.toString() : t.toString()));

                      // Lấy chi tiết tasks
                      const tasksResult = await this.broker.call('tasks.find', {
                        query: {
                          _id: {$in: taskIds},
                          isDeleted: {$ne: true},
                        },
                        sort: 'orderInScenario',
                      });
                      taskEntities = tasksResult || [];
                    }
                    this.logger.info(`Lấy được ${taskIds.length} tasks từ AI scenario cho courseId: ${courseId}.`);
                  } else {
                    this.logger.warn(
                      `Không tìm thấy AI scenario với aiPersonaId ${aiPersonaId} cho course ${courseId}`,
                    );
                  }
                } catch (scenarioErr) {
                  this.logger.error(`Lỗi khi lấy AI scenarios cho courseId ${courseId}:`, scenarioErr);
                }
              }
            } catch (err) {
              this.logger.error(`Lỗi khi lấy courseData cho courseId ${courseId}:`, err);
            }
          }

          if (!aiScenario) {
            this.logger.error(`Không thể tìm thấy AI scenario với aiPersonaId ${aiPersonaId} cho course ${courseId}.`);
            throw new MoleculerClientError('Không tìm thấy AI scenario phù hợp cho phiên roleplay.', 400);
          }

          this.logger.info(
            'Debug userId - ctx.params.userId:',
            ctx.params.userId,
            'ctx.meta.user?._id:',
            ctx.meta.user?._id,
          );

          const actingUserId = studentId || (ctx.meta.user ? ctx.meta.user._id.toString() : null);
          const createdById = studentId || (ctx.meta.user ? ctx.meta.user._id.toString() : null);

          if (!actingUserId) {
            this.logger.error(`Không thể xác định người dùng thực hiện (actingUserId) cho phiên ${sessionId}.`);
            throw new MoleculerClientError('Không xác định được người dùng.', 400);
          }

          console.log(
            'courseId',
            courseId,
            'studentId (actingUserId)',
            actingUserId,
            'aiPersonaId (từ client)',
            aiPersonaId,
            'aiScenario',
            aiScenario?._id,
          );

          const sessionEntity = await this.adapter.insert({
            clientSessionId: sessionId,
            userId: actingUserId,
            personaId: aiPersonaId,
            socketId,
            startedAt: new Date(),
            isActive: true,
            studentId: studentId,
            createdBy: createdById,
            courseId,
            taskIds,
            aiScenarioId: aiScenario?._id, // Lưu AI scenario ID để thống kê
          });

          if (connectionState.has(socketId)) {
            const state = connectionState.get(socketId);
            state.sessionId = sessionEntity._id.toString();
            state.userId = actingUserId;
            state.isSessionActive = true;
            state.courseId = courseId;
            state.taskEntities = taskEntities;
            state.personaId = aiPersonaId; // Gán aiPersonaId từ client vào state
            state.aiScenarioId = aiScenario?._id; // Lưu AI scenario ID
          }

          return {success: true, session: sessionEntity};
        } catch (error) {
          this.logger.error('Lỗi khi bắt đầu phiên roleplay:', error);
          if (error instanceof MoleculerClientError) throw error;
          throw new MoleculerClientError('Lỗi máy chủ khi bắt đầu phiên roleplay.', 500);
        }
      },
    },

    // Xử lý kết thúc phiên roleplay
    endSession: {
      async handler(ctx) {
        try {
          const {sessionId, socketId, reason} = ctx.params; // sessionId ở đây là _id của session trong DB
          this.logger.info(`Kết thúc phiên roleplay: ${sessionId}, lý do: ${reason}`);

          const sessionEntity = await this.adapter.findById(sessionId);
          if (!sessionEntity) {
            this.logger.warn(`Phiên roleplay với ID ${sessionId} không tìm thấy trong DB khi kết thúc.`);
            const stateFromMap = connectionState.get(socketId);
            if (stateFromMap && stateFromMap.isSessionActive) {
              this.logger.info(
                `Sử dụng trạng thái từ connectionState cho socketId ${socketId} do không tìm thấy session DB.`,
              );
              // Kiểm tra xem có audio chunks để lưu không, ngay cả khi sessionEntity không tìm thấy
              if (stateFromMap.allAudioChunksForSession && stateFromMap.allAudioChunksForSession.length > 0) {
                try {
                  await this.saveAudioFile(stateFromMap); // Gọi saveAudioFile nếu có audio
                  this.logger.info(
                    `Audio cho session (không tìm thấy DB) ${sessionId} từ socket ${socketId} đã được thử lưu.`,
                  );
                } catch (e) {
                  this.logger.error(`Lỗi khi cố gắng lưu audio cho session (không tìm thấy DB) ${sessionId}:`, e);
                }
              }
            }
            return {
              success: true,
              message: 'Session not found in DB or already ended, audio processing may be based on transient state.',
            };
          }

          // Phần xử lý lưu audio được tách gọn bằng mixin
          const state = connectionState.get(socketId);
          let recordingFileId = null;
          let aiRecordingFileId = null;
          if (state && state.sessionId === sessionId.toString()) {
            try {
              const userInfo = await this.saveAudioFile(state);
              recordingFileId = userInfo?.fileId || null;
            } catch (e) {
              this.logger.error(`Lỗi lưu audio người dùng trong endSession: ${sessionId}`, e);
            }
            try {
              const aiInfo = await this.saveAiAudioFile(state);
              aiRecordingFileId = aiInfo?.fileId || null;
            } catch (e) {
              this.logger.error(`Lỗi lưu audio AI trong endSession: ${sessionId}`, e);
            }
          }

          // Tính thời gian kết thúc và duration bằng helper
          const endTime = new Date();
          const duration = this.calculateSessionDuration(sessionEntity.startedAt, endTime);
          // Chuẩn bị transcripts bằng helper
          const transcripts = this.buildSessionTranscripts(connectionState.get(socketId));
          // Cập nhật trạng thái phiên trong DB
          await this.adapter.updateById(sessionEntity._id, {
            $set: {
              status: 'completed',
              endTime,
              duration,
              isCompleted: true,
              transcripts,
              aiRecordingId: aiRecordingFileId,
            },
          });

          // Phase 3: Phát sự kiện để service phân tích xử lý
          const updatedSession = await this.adapter.findById(sessionEntity._id); // Lấy session đã cập nhật
          this.broker.emit('roleplay.session.completed_for_analysis', {
            sessionId: sessionEntity._id.toString(),
            sessionData: updatedSession, // Gửi kèm dữ liệu session nếu service analysis cần
          });
          this.logger.info(
            `Đã phát sự kiện 'roleplay.session.completed_for_analysis' cho sessionId: ${sessionEntity._id}`,
          );
          connectionState.delete(socketId);
          return {success: true, session: updatedSession}; // Trả về session đã cập nhật
        } catch (error) {
          this.logger.error('Lỗi khi kết thúc phiên roleplay:', error);
          throw error;
        }
      },
    },

    // Xử lý khi client ngắt kết nối
    handleDisconnectedSession: {
      visibility: 'private',
      async handler(ctx) {
        const {sessionId, socketId, state} = ctx.params; // sessionId là state.sessionId (_id của DB)

        const sessionEntity = await this.adapter.findById(sessionId);

        if (sessionEntity && sessionEntity.isActive) {
          await this.adapter.updateById(sessionEntity._id, {
            $set: {
              isActive: false,
              endedAt: new Date(),
              endReason: 'client_disconnected',
              // Không cập nhật aiRecordingId ở đây vì handleDisconnectedSession có thể không có đủ context hoặc audio AI đầy đủ
              // Không có sự phụ thuộc vào taskId ở đây
            },
          });
          this.logger.info(`Phiên ${sessionId} được cập nhật là ngắt kết nối.`);
        } else if (sessionEntity && !sessionEntity.isActive) {
          this.logger.info(`Phiên ${sessionId} đã được đánh dấu là không hoạt động trước đó.`);
        } else {
          this.logger.warn(`Phiên ${sessionId} không tìm thấy trong DB khi xử lý ngắt kết nối cho socket ${socketId}.`);
        }

        // Lưu audio của người dùng khi ngắt kết nối
        if (state && state.allAudioChunksForSession && state.allAudioChunksForSession.length > 0) {
          this.logger.info(`Lưu audio của người dùng cho phiên bị ngắt kết nối ${sessionId} từ socket ${socketId}`);
          try {
            await this.saveAudioFile(state);
          } catch (saveError) {
            this.logger.error(`Lỗi khi lưu audio của người dùng cho phiên bị ngắt kết nối ${sessionId}:`, saveError);
          }
        } else {
          this.logger.info(
            `Không có audio data của người dùng để lưu cho phiên bị ngắt kết nối ${sessionId} từ socket ${socketId}`,
          );
        }

        // Lưu audio của AI khi ngắt kết nối
        if (state && state.allAiAudioChunksForSession && state.allAiAudioChunksForSession.length > 0) {
          this.logger.info(`Lưu audio của AI cho phiên bị ngắt kết nối ${sessionId} từ socket ${socketId}`);
          try {
            await this.saveAiAudioFile(state); // Sử dụng hàm mới hoặc hàm đã sửa đổi
          } catch (saveError) {
            this.logger.error(`Lỗi khi lưu audio của AI cho phiên bị ngắt kết nối ${sessionId}:`, saveError);
          }
        } else {
          this.logger.info(
            `Không có audio data của AI để lưu cho phiên bị ngắt kết nối ${sessionId} từ socket ${socketId}`,
          );
        }
        return {success: true};
      },
    },
  },

  events: {
    'roleplay.client.connected': {
      async handler(socket) {
        const connectionId = socket.id;
        this.logger.info(`RolePlay client connected: ${connectionId}`);

        // Khởi tạo trạng thái cho kết nối
        const initialState = createConnectionState();
        initialState.socket = socket; // Lưu tham chiếu socket
        connectionState.set(connectionId, initialState);

        // Xử lý các sự kiện từ client
        socket.on('client:start_session', async data => {
          this.logger.info(`Nhận client:start_session từ ${connectionId}`, data);
          try {
            // Xử lý bắt đầu phiên roleplay
            const result = await this.actions.startSession({
              sessionId: data.sessionId, // client-generated UUID
              userId: data.userId, // userId từ client (có thể là studentId hoặc người dùng đang tương tác)
              studentId: data.studentId, // studentId cụ thể cho session này
              courseId: data.courseId,
              aiPersonaId: data.aiPersonaId, // aiPersonaId từ client
              socketId: connectionId,
            });

            const session = result.session; // session entity từ DB, chứa _id và personaId đã được xác định
            const state = connectionState.get(connectionId);

            // Cập nhật trạng thái kết nối với thông tin từ session entity đã được action xử lý
            state.sessionId = session._id.toString();
            state.userId = session.userId ? session.userId.toString() : null;
            state.personaId = session.personaId ? session.personaId.toString() : null; // QUAN TRỌNG: Lấy personaId từ session entity (đã được action startSession xử lý và lưu vào DB)
            state.isSessionActive = true;
            state.courseId = session.courseId ? session.courseId.toString() : null;
            state.studentId = session.studentId ? session.studentId.toString() : null;
            state.clientSessionId = data.sessionId;
            // Lấy thông tin course
            state.course = await this.broker.call('courses.get', {
              id: state.courseId,
            });

            // Lấy thông tin scenario từ session state (đã được lưu trong startSession)
            if (state.aiScenarioId) {
              try {
                state.aiScenario = await this.broker.call('aiscenarios.get', {
                  id: state.aiScenarioId,
                });
              } catch (error) {
                this.logger.warn(`Không thể lấy AI scenario ${state.aiScenarioId}:`, error.message);
              }
            }
            state.persona = await this.broker.call('aipersonas.get', {id: state.personaId});

            if (!state.personaId) {
              this.logger.error(
                `[${state.sessionId}] Critical error: state.personaId is undefined sau khi action startSession đã chạy. Session entity:`,
                session,
                'Client data:',
                data,
              );
              socket.emit('server:error', {message: 'Lỗi nghiêm trọng: Không thể xác định AI Persona từ khóa học.'});
              return;
            }
            const messages = await this.generateConversationPrompt(state);
            state.conversationHistory = messages; // Sử dụng toàn bộ messages (bao gồm system prompt và history ban đầu)

            // Kiểm tra tin nhắn cuối trong conversationHistory và xử lý TTS nếu là của assistant
            const lastMessage = state.conversationHistory[state.conversationHistory.length - 1];
            if (
              lastMessage &&
              lastMessage.role === 'assistant' &&
              lastMessage.content &&
              lastMessage.content.trim() !== ''
            ) {
              this.logger.info(
                `[${state.sessionId}] Last message in initial history is from assistant, processing TTS for: "${lastMessage.content}"`,
              );
              socket.emit('server:ai_tts_started', {sessionId: state.sessionId});
              try {
                const audioBuffer = await this.processSingleSentenceToSpeech(state, lastMessage.content, socket);
                if (audioBuffer) {
                  this.logger.info(`[${state.sessionId}] Successfully processed TTS for initial assistant message.`);
                } else {
                  this.logger.warn(
                    `[${state.sessionId}] TTS processing for initial assistant message returned no audio buffer.`,
                  );
                }
              } catch (ttsError) {
                this.logger.error(`[${state.sessionId}] Error processing TTS for initial assistant message:`, ttsError);
                socket.emit('server:error', {
                  sessionId: state.sessionId,
                  message: 'Lỗi khi tạo lời nói cho tin nhắn đầu tiên của trợ lý.',
                });
              } finally {
                socket.emit('server:ai_tts_completed', {sessionId: state.sessionId});
              }
            } else if (
              lastMessage &&
              lastMessage.role === 'user' &&
              lastMessage.content &&
              lastMessage.content.trim() !== ''
            ) {
              this.logger.info(
                `[${state.sessionId}] Last message in initial history is from user, processing AI response for: "${lastMessage.content}"`,
              );
              // Gọi processLLMResponseStreamAll để AI xử lý và phản hồi
              // Đảm bảo state.socket là socket của client hiện tại
              if (state.socket) {
                await this.processLLMResponseStreamAll(state, state.socket);
              } else {
                this.logger.error(
                  `[${state.sessionId}] Cannot process initial user message as state.socket is not defined.`,
                );
                socket.emit('server:error', {
                  sessionId: state.sessionId,
                  message: 'Lỗi máy chủ: Không thể xử lý tin nhắn đầu tiên của bạn do thiếu thông tin kết nối.',
                });
              }
            }

            socket.emit('server:session_started', session);
          } catch (err) {
            this.logger.error('Lỗi khi bắt đầu phiên roleplay:', err);
            socket.emit('server:error', {message: 'Không thể bắt đầu phiên roleplay'});
          }
        });

        socket.on('client:student_speech_chunk', async data => {
          try {
            const state = connectionState.get(connectionId);
            if (!state || !state.isSessionActive) {
              this.logger.warn(
                'client:student_speech_chunk: Nhận được chunk khi session không active hoặc không có state',
              );
              return;
            }
            await this._handleStudentAudioChunk(state, data.audioChunk, data.format, socket);
          } catch (err) {
            this.logger.error('Lỗi khi xử lý client:student_speech_chunk event:', err);
          }
        });

        socket.on('client:end_session', async data => {
          this.logger.info(`Nhận client:end_session từ ${connectionId}`, data); // data chứa sessionId (client UUID) và reason
          try {
            const state = connectionState.get(connectionId);
            if (!state || !state.sessionId) {
              this.logger.warn(
                'client:end_session: Không tìm thấy state hoặc sessionId cho connectionId:',
                connectionId,
              );
              socket.emit('server:error', {message: 'Không thể kết thúc phiên, thông tin phiên không hợp lệ.'});
              return;
            }

            state.isProcessingSpeech = false;

            const result = await this.actions.endSession({
              sessionId: state.sessionId, // Sử dụng DB sessionId từ state
              socketId: connectionId,
              reason: data.reason,
            });

            socket.emit('server:session_ended', result);
          } catch (err) {
            this.logger.error('Lỗi khi xử lý client:end_session event:', err);
            socket.emit('server:error', {message: 'Lỗi khi kết thúc phiên roleplay'});
          }
        });

        socket.on('disconnect', async reason => {
          this.logger.info(`RolePlay client ngắt kết nối: ${connectionId}. Lý do: ${reason}`);

          const state = connectionState.get(connectionId);
          if (state && state.sessionId) {
            // sessionId là _id của DB
            try {
              state.isProcessingSpeech = false;

              await this.actions.handleDisconnectedSession({
                sessionId: state.sessionId,
                socketId: connectionId,
                state: state, // Truyền toàn bộ state
              });
            } catch (err) {
              this.logger.error('Lỗi khi gọi handleDisconnectedSession:', err);
            }
          } else if (state) {
            this.logger.info(`Client ${connectionId} ngắt kết nối nhưng không có DB sessionId trong state.`);
          } else {
            this.logger.info(`Client ${connectionId} ngắt kết nối nhưng không tìm thấy connectionState.`);
          }

          // connectionState.delete(connectionId);
          this.logger.info(`Đã xóa connectionState cho ${connectionId}`);
        });

        // Thông báo cho client rằng server đã sẵn sàng
        socket.emit('server:ready', {status: 'ready'});
      },
    },

    // Event handlers cho STT streaming từ speechprocessing.service
    'speech.stream.recognizing': {
      group: 'local', // Chỉ xử lý trên node đã gọi speechprocessing
      async handler(payload) {
        const recognizedEventId = `recognizedEvent_${payload.sessionId}_${Date.now()}`;
        console.time(recognizedEventId);
        const {streamId, sessionId: eventSessionId, text} = payload;
        // console.log(`Nhận speech.stream.recognizing: streamId=${streamId}, text="${text}"`);

        const state = this.findStateBySttStreamIdOrSessionIdOnly(streamId, eventSessionId);

        if (state && state.isSessionActive && state.socket) {
          // state.currentStudentTranscript += (text + " "); // Có thể không cần nếu client tự xử lý nối
          state.socket.emit('server:student_text_response', {
            sessionId: state.sessionId,
            text: text, // Gửi phần text mới nhất
            role: 'user',
            isFinal: false,
          });
        } else {
          this.logger.warn(
            `Không tìm thấy state hoặc socket hợp lệ cho speech.stream.recognizing, streamId: ${streamId}, eventSessionId: ${eventSessionId}`,
          );
        }
        console.timeEnd(recognizedEventId);
      },
    },

    'speech.stream.recognized': {
      group: 'local',
      async handler(payload) {
        const recognizedEventId = `recognizedEvent_${payload.sessionId}_${Date.now()}`;
        console.time(recognizedEventId);
        const {streamId, sessionId: eventSessionId, text, duration, offset} = payload;
        console.log(`Nhận speech.stream.recognized: streamId=${streamId}, text="${text}", duration=${duration}`);

        const state = this.findStateBySttStreamIdOrSessionIdOnly(streamId, eventSessionId);

        if (state && state.isSessionActive) {
          let incorrectTexts = ['Phẩy.'];

          if (text && text.trim() !== '' && !incorrectTexts.includes(text.trim())) {
            state.currentStudentTranscript += text + ' ';
          } else {
            this.logger.info(
              `speech.stream.recognized: Text rỗng hoặc chỉ chứa khoảng trắng, bỏ qua xử lý LLM. StreamId: ${streamId}`,
            );
          }
        } else {
          this.logger.warn(
            `Không tìm thấy state hoặc socket hợp lệ cho speech.stream.recognized, streamId: ${streamId}, eventSessionId: ${eventSessionId}`,
          );
        }
        console.timeEnd(recognizedEventId);
      },
    },

    'speech.stream.nomatch': {
      group: 'local',
      async handler(payload) {
        const {streamId, sessionId: eventSessionId, reason} = payload;
        this.logger.warn(`Nhận speech.stream.nomatch: streamId=${streamId}, reason="${reason}"`);
        const state = this.findStateBySttStreamIdOrSessionIdOnly(streamId, eventSessionId);

        if (state && state.isSessionActive) {
          if (state.socket) {
            state.socket.emit('server:stt_nomatch', {
              sessionId: state.sessionId,
              message: 'Không nhận dạng được giọng nói.',
            });
          }
        }
      },
    },

    'speech.stream.error': {
      group: 'local',
      async handler(payload) {
        const {streamId, sessionId: eventSessionId, errorCode, errorDetails} = payload;
        this.logger.error(
          `Nhận speech.stream.error: streamId=${streamId}, code=${errorCode}, details="${errorDetails}"`,
        );
        const state = this.findStateBySttStreamIdOrSessionIdOnly(streamId, eventSessionId);

        if (state && state.isSessionActive) {
          if (state.socket) {
            state.socket.emit('server:error', {
              sessionId: state.sessionId,
              message: `Lỗi STT streaming: ${errorDetails}`,
            });
          }
          if (state.sttStreamId === streamId) {
            try {
              await this.broker.call('roleplay.speechprocessing.closeSpeechStream', {streamId: state.sttStreamId});
            } catch (closeError) {
              this.logger.error(
                `Lỗi khi cố gắng đóng speech stream ${state.sttStreamId} sau khi nhận lỗi từ stream:`,
                closeError,
              );
            }
            state.sttStreamId = null;
            state.isStudentSpeaking = false;
          }
        }
      },
    },

    'roleplay.analysises.created': {
      group: 'local',
      async handler(payload) {
        const {analysis} = payload;
        this.logger.info(`Nhận roleplay.analysises.created: ${analysis._id}`);
      },
    },

    'roleplay.analysis.completed': {
      group: 'local', // Hoặc tên group phù hợp
      async handler(payload) {
        const {sessionId, analysisId} = payload;
        this.logger.info(
          `Nhận sự kiện 'roleplay.analysis.completed' cho sessionId: ${sessionId} với analysisId: ${analysisId}`,
        );
        try {
          const session = await this.adapter.findById(sessionId);
          if (session) {
            await this.adapter.updateById(sessionId, {
              $set: {
                analysisId: analysisId,
                status: 'analyzed', // Cập nhật trạng thái session là đã phân tích
              },
            });
            this.logger.info(`Đã cập nhật analysisId ${analysisId} và status 'analyzed' cho session ${sessionId}.`);
          } else {
            this.logger.warn(`Không tìm thấy session ${sessionId} để cập nhật analysisId.`);
          }
        } catch (error) {
          this.logger.error(`Lỗi khi xử lý sự kiện 'roleplay.analysis.completed' cho session ${sessionId}:`, error);
        }
      },
    },
  },

  methods: {
    // Đổi tên helper method và chỉ trả về state
    findStateBySttStreamIdOrSessionIdOnly(sttStreamId, sessionId) {
      if (sttStreamId) {
        for (const state of connectionState.values()) {
          // Duyệt qua values thay vì entries
          if (state.sttStreamId === sttStreamId) {
            return state;
          }
        }
      }
      if (sessionId) {
        for (const state of connectionState.values()) {
          // Duyệt qua values
          if (state.sessionId === sessionId && state.isSessionActive) {
            if (sttStreamId && state.sttStreamId && state.sttStreamId !== sttStreamId) {
              continue;
            }
            this.logger.debug(
              `Tìm thấy state bằng sessionId ${sessionId} khi sttStreamId ${sttStreamId} không khớp hoặc không có.`,
            );
            return state;
          }
        }
      }
      this.logger.warn(`Không tìm thấy state cho sttStreamId: ${sttStreamId} hoặc sessionId: ${sessionId}`);
      return null; // Trả về null nếu không tìm thấy
    },

    async generateConversationPrompt(state) {
      // Lấy thông tin persona và course
      if (!state.courseId || !state.personaId) {
        this.logger.error('generateConversationPrompt: Thiếu courseId hoặc personaId trong state', state);
        // Trả về một prompt mặc định hoặc throw lỗi
        return [{role: 'system', content: 'Lỗi: Không thể tải thông tin khóa học hoặc persona.'}];
      }
      const {course, persona, aiScenario} = state;
      if (!course || !persona) {
        this.logger.error(`Không tìm thấy Course (ID: ${state.courseId}) hoặc Persona (ID: ${state.personaId})`);
        return [{role: 'system', content: 'Lỗi: Không thể tải thông tin khóa học hoặc persona.'}];
      }

      try {
        // Lọc bỏ các system prompt trước đó để tránh lặp
        const nonSystemHistory = state.conversationHistory.filter(m => m.role !== 'system');

        // Chuẩn bị dữ liệu course với thông tin từ scenario
        const enrichedCourse = {
          ...course,
          // Thêm thông tin từ scenario nếu có
          roleplayInstructionId: aiScenario?.roleplayInstructionId || course.roleplayInstructionId,
          taskIds: aiScenario?.taskIds || course.taskIds || [],
        };

        // Nếu taskIds là array của IDs, cần lấy thông tin chi tiết của tasks
        if (enrichedCourse.taskIds && enrichedCourse.taskIds.length > 0) {
          try {
            const taskIds = enrichedCourse.taskIds.map(t => (typeof t === 'object' ? t._id || t : t));
            const tasksResult = await this.broker.call('tasks.find', {
              query: {
                _id: {$in: taskIds},
                isDeleted: {$ne: true},
              },
              sort: 'orderInScenario',
            });

            if (tasksResult && tasksResult.length > 0) {
              enrichedCourse.taskIds = tasksResult;
            }
          } catch (taskError) {
            this.logger.warn(`Không thể lấy chi tiết tasks: ${taskError.message}`);
          }
        }

        // Sử dụng action createPersonaPrompt từ openai.service.js để tạo prompt
        const messages = await this.broker.call('roleplay.openai.createPersonaPrompt', {
          persona: persona,
          course: enrichedCourse,
          conversation: nonSystemHistory,
        });

        // Nếu có introduction, thêm vào sau system prompt và trước lịch sử hội thoại
        if (course.introduction) {
          // Tìm vị trí để chèn introduction (sau system prompt)
          const systemIndex = messages.findIndex(m => m.role === 'system');
          if (systemIndex !== -1) {
            messages.splice(systemIndex + 1, 0, {role: 'assistant', content: course.introduction});
          } else {
            // Nếu không tìm thấy system prompt, thêm vào đầu
            messages.unshift({role: 'assistant', content: course.introduction});
          }
        }
        return messages;
      } catch (error) {
        this.logger.error(`Lỗi khi gọi createPersonaPrompt: ${error.message}`, error);
        // Trả về một prompt mặc định trong trường hợp lỗi
        return [{role: 'system', content: 'Lỗi: Không thể tạo prompt cho AI Persona.'}];
      }
    },

    // Stream audio với rate control để hỗ trợ ngắt lời hiệu quả

    createVersionHistoryEntry(exercise, userId) {
      const {name, tag, difficulty, type, segments, timeLimit, transcript, status, audioId} = exercise;
      return {name, tag, difficulty, type, segments, timeLimit, transcript, status, audioId, updatedBy: userId};
    },

    constructParams(params, query, sort) {
      return {
        ...this.extractParamsList(params),
        searchFields: 'tag,name',
        query: JSON.stringify(query),
        fields: 'name tag difficulty type audioId avatarId _id segments',
        sort,
        populate: [],
      };
    },
  },

  created() {},

  async started() {
    this.createFolderIfNotExist(storageDir);
    console.log('#####################started');
  },

  async stopped() {
    console.log('#####################stopped');
  },
};
