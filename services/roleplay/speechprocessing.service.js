"use strict";

const FunctionsCommon = require("../../mixins/functionsCommon.mixin");
const FileMixin = require("../../mixins/file.mixin");
const BaseService = require("../../mixins/baseService.mixin");
const { MoleculerClientError } = require("moleculer").Errors;
const path = require("path");
const fs = require("fs");
const sdk = require("microsoft-cognitiveservices-speech-sdk");
const ffmpeg = require("fluent-ffmpeg");
const wav = require("wav");
const { v4: uuidv4 } = require("uuid");
const i18next = require("i18next");
const OpenAI = require("openai");
const { PassThrough } = require('stream');

// Thêm import cho GoogleGenAI
let LoadedGoogleGenAIClass = null;

// Th<PERSON> mục lưu trữ tạm thời cho các tệp âm thanh
const storageDir = path.join(__dirname, "storage");

// Map để lưu trữ các speech recognizer đang hoạt động cho streaming
const speechStreams = new Map();

module.exports = {
  name: "roleplay.speechprocessing",
  mixins: [FunctionsCommon, FileMixin, BaseService],

  settings: {
    // Cấu hình mặc định
    tts: {
      provider: "openai", // openai hoặc microsoft
      speed: 1.0          // Tốc độ nói bình thường
      // Có thể thêm cấu hình riêng cho OpenAI nếu cần, ví dụ:
      // openai_model: "tts-1",
      // openai_voice: "alloy",
      // openai_speed: 1.0
    },
    stt: {
      provider: "openai", // openai (Whisper) hoặc microsoft
      language: "vi-VN",  // Ngôn ngữ mặc định
      model: "whisper"    // Model mặc định
    }
  },

  actions: {
    /**
     * Chuyển đổi văn bản thành giọng nói (TTS)
     */
    textToSpeech: {
      params: {
        text: "string",
        voice: { type: "string", optional: true },
        speed: { type: "number", optional: true },
        provider: { type: "string", optional: true },
        format: { type: "string", optional: true, default: "mp3" },
        model: { type: "string", optional: true }, // Thêm model để người dùng có thể truyền
        paramInstructions: { type: "string", optional: true } // Thêm paramInstructions để hỗ trợ voice style
      },
      async handler(ctx) {
        const {
          text,
          voice: paramVoice, // Sử dụng tên khác để rõ ràng
          speed: paramSpeed,
          provider = this.settings.tts.provider,
          format = "mp3",
          model: paramModel,
          paramInstructions: paramInstructions // Thêm paramInstructions
        } = ctx.params;
        console.log("#####################provider", provider);
        // Xử lý theo provider được chọn
        if (provider === "openai") {
          // Truyền model và paramInstructions (nếu có) vào openaiTTS
          return this.openaiTTS(text, paramVoice, paramSpeed, format, paramModel, paramInstructions);
        } else if (provider === "microsoft") {
          return this.microsoftTTS(text, paramVoice, paramSpeed, format); // Microsoft TTS không dùng model, instructions từ params này
        } else if (provider === "google_gemini") {
          if (!LoadedGoogleGenAIClass) {
            throw new MoleculerClientError("Google Gemini SDK (@google/genai) is not installed or failed to load. Please check installation and server logs.", 500, "MISSING_GEMINI_SDK");
          }
          return this.googleGeminiTTS(text, paramVoice, paramSpeed, format);
        } else {
          throw new MoleculerClientError(`Provider TTS không hỗ trợ: ${provider}`, 400);
        }
      }
    },

    /**
     * Chuyển đổi giọng nói thành văn bản (STT)
     */
    speechToText: {
      params: {
        audioBuffer: { type: "any", optional: true }, // Buffer âm thanh
        audioPath: { type: "string", optional: true }, // Hoặc đường dẫn tới file âm thanh
        language: { type: "string", optional: true },
        provider: { type: "string", optional: true }
      },
      async handler(ctx) {
        const {
          audioBuffer,
          audioPath,
          language = this.settings.stt.language,
          provider = this.settings.stt.provider
        } = ctx.params;

        // Kiểm tra đầu vào
        if (!audioBuffer && !audioPath) {
          throw new MoleculerClientError("Cần cung cấp audioBuffer hoặc audioPath", 400);
        }

        // Xử lý theo provider được chọn
        if (provider === "openai") {
          return this.openaiSTT(audioBuffer, audioPath, language);
        } else if (provider === "microsoft") {
          return this.microsoftSTT(audioBuffer, audioPath, language);
        } else {
          throw new MoleculerClientError(`Provider STT không hỗ trợ: ${provider}`, 400);
        }
      }
    },
    /**
     * Phân tích tốc độ nói và từ đệm từ transcript
     */
    analyzeSpeech: {
      params: {
        transcript: "string",
        audioLength: "number" // Thời lượng âm thanh tính bằng giây
      },
      async handler(ctx) {
        const { transcript, audioLength } = ctx.params;

        try {
          // Tính tốc độ nói (số từ trên phút)
          const words = transcript.trim().split(/\s+/);
          const wordCount = words.length;
          const minuteLength = audioLength / 60;
          const wordsPerMinute = Math.round(wordCount / minuteLength);

          // Phân tích từ đệm (ví dụ: "ừm", "à", "vậy đó", v.v.)
          const fillerWords = ["ừm", "ừ", "à", "vậy đó", "thì là", "kiểu như", "kiểu", "nói chung là"];
          const fillerWordCounts = {};
          let totalFillerWords = 0;

          fillerWords.forEach(word => {
            const regex = new RegExp(`\\b${word}\\b`, 'gi');
            const matches = transcript.match(regex);
            const count = matches ? matches.length : 0;
            if (count > 0) {
              fillerWordCounts[word] = count;
              totalFillerWords += count;
            }
          });

          // Phân tích độ dài câu
          const sentences = transcript.split(/[.!?]+/).filter(s => s.trim().length > 0);
          const sentenceLengths = sentences.map(s => s.trim().split(/\s+/).length);
          const avgSentenceLength = sentenceLengths.reduce((a, b) => a + b, 0) / sentenceLengths.length;

          return {
            wordsPerMinute,
            totalWords: wordCount,
            duration: audioLength,
            fillerWords: {
              total: totalFillerWords,
              details: fillerWordCounts,
              percentage: (totalFillerWords / wordCount) * 100
            },
            sentences: {
              count: sentences.length,
              averageLength: avgSentenceLength,
              lengthDistribution: sentenceLengths
            }
          };
        } catch (error) {
          this.logger.error("Lỗi khi phân tích giọng nói:", error);
          throw new MoleculerClientError("Không thể phân tích giọng nói", 500);
        }
      }
    },

    initializeSpeechStream: {
      params: {
        language: { type: "string", optional: true, default: "vi-VN" },
        sessionId: { type: "string" } // Để liên kết với session của roleplay
      },
      async handler(ctx) {
        const { language, sessionId } = ctx.params;
        const streamId = uuidv4();

        try {
          const { speechKey, serviceRegion } = await this.broker.call("settings.findOne");
          if (!speechKey || !serviceRegion) {
            throw new MoleculerClientError("Thiếu cấu hình Microsoft Speech SDK trong settings.", 500, "MISSING_SPEECH_CONFIG");
          }

          const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, serviceRegion);
          speechConfig.speechRecognitionLanguage = language;
          // Cấu hình output chi tiết nếu cần (ví dụ: word level timings)
          // speechConfig.outputFormat = sdk.OutputFormat.Detailed;

          // Kiểm tra sdk.AudioFormat và các thành phần cần thiết
          if (!sdk || !sdk.AudioStreamFormat || typeof sdk.AudioStreamFormat.getWaveFormat !== 'function' || !sdk.AudioFormatTag || !sdk.AudioFormatTag.PCM) {
            this.logger.error("Microsoft Speech SDK AudioStreamFormat or AudioFormatTag.PCM is not available.");
            throw new MoleculerClientError("Microsoft Speech SDK AudioStreamFormat or AudioFormatTag.PCM not available.", 500, "SDK_STREAMFORMAT_ERROR");
          }
          // Tạo AudioStreamFormat một cách tường minh thay vì dùng getDefaultInputFormat()
          const audioFormat = sdk.AudioStreamFormat.getWaveFormat(16000, 16, 1, sdk.AudioFormatTag.PCM); // PCM 16kHz, 16-bit, mono
          const pushStream = sdk.AudioInputStream.createPushStream(audioFormat);

          const audioConfig = sdk.AudioConfig.fromStreamInput(pushStream);
          const recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);

          recognizer.recognizing = (s, e) => {
            if (e.result.reason === sdk.ResultReason.RecognizingSpeech) {
              this.broker.emit("speech.stream.recognizing", {
                streamId,
                sessionId,
                text: e.result.text,
                isFinal: false
              });
            }
          };

          recognizer.recognized = (s, e) => {
            if (e.result.reason === sdk.ResultReason.RecognizedSpeech) {
              this.broker.emit("speech.stream.recognized", {
                streamId,
                sessionId,
                text: e.result.text,
                isFinal: true,
                duration: e.result.duration, // Thời gian của đoạn nói này
                offset: e.result.offset // Offset từ đầu stream
              });
            } else if (e.result.reason === sdk.ResultReason.NoMatch) {
              this.broker.emit("speech.stream.nomatch", {
                streamId,
                sessionId,
                reason: "No speech could be recognized."
              });
            }
          };

          recognizer.canceled = (s, e) => {
            let reason = e.reason;
            if (reason === sdk.CancellationReason.Error) {
              this.logger.error(`SPEECH STREAM CANCELED: ErrorCode=${e.errorCode}, ErrorDetails=${e.errorDetails}, StreamId=${streamId}`);
              this.broker.emit("speech.stream.error", {
                streamId,
                sessionId,
                errorCode: e.errorCode,
                errorDetails: e.errorDetails
              });
            }
            // recognizer.stopContinuousRecognitionAsync(); // Sẽ được gọi bởi closeSpeechStream
            // speechStreams.delete(streamId); // Sẽ được gọi bởi closeSpeechStream
          };

          recognizer.sessionStarted = (s, e) => {
            this.logger.info(`Speech stream session started: ${e.sessionId}, StreamId: ${streamId}`);
          };

          recognizer.sessionStopped = (s, e) => {
            this.logger.info(`Speech stream session stopped: ${e.sessionId}, StreamId: ${streamId}`);
            // recognizer.stopContinuousRecognitionAsync(); // Sẽ được gọi bởi closeSpeechStream
            // speechStreams.delete(streamId); // Sẽ được gọi bởi closeSpeechStream
          };

          recognizer.startContinuousRecognitionAsync(
            () => {
              this.logger.info(`Continuous recognition started for stream: ${streamId}`);
            },
            (err) => {
              this.logger.error(`Error starting continuous recognition for stream ${streamId}: ${err}`);
              speechStreams.delete(streamId); // Dọn dẹp nếu không start được
              throw new MoleculerClientError(`Failed to start speech recognition stream: ${err}`, 500, "STREAM_START_ERROR");
            }
          );

          speechStreams.set(streamId, { recognizer, pushStream, sessionId });
          return { streamId, success: true };

        } catch (error) {
          this.logger.error("Error initializing speech stream:", error);
          if (error instanceof MoleculerClientError) throw error;
          throw new MoleculerClientError("Failed to initialize speech stream.", 500, "STREAM_INIT_ERROR");
        }
      }
    },

    pushAudioToStream: {
      params: {
        streamId: "string",
        audioChunk: "any" // Buffer
      },
      async handler(ctx) {
        const { streamId, audioChunk } = ctx.params;
        const streamData = speechStreams.get(streamId);

        if (!streamData) {
          throw new MoleculerClientError(`Speech stream not found: ${streamId}`, 404, "STREAM_NOT_FOUND");
        }

        // Đảm bảo audioChunk là Buffer
        const buffer = Buffer.isBuffer(audioChunk) ? audioChunk : Buffer.from(audioChunk);

        streamData.pushStream.write(buffer);
        return { success: true, bytesWritten: buffer.length };
      }
    },

    closeSpeechStream: {
      params: {
        streamId: "string"
      },
      async handler(ctx) {
        const { streamId } = ctx.params;
        const streamData = speechStreams.get(streamId);

        if (!streamData) {
          this.logger.warn(`Attempted to close non-existent or already closed stream: ${streamId}`);
          return { success: false, message: "Stream not found or already closed." };
        }

        try {
          streamData.pushStream.close(); // Báo hiệu kết thúc audio input
          await new Promise(resolve => streamData.recognizer.stopContinuousRecognitionAsync(resolve, resolve)); // Chờ recognizer dừng hẳn

          this.logger.info(`Speech stream closed and recognizer stopped for stream: ${streamId}`);
        } catch (error) {
          this.logger.error(`Error stopping recognizer for stream ${streamId}:`, error);
          // Dù lỗi vẫn cố gắng dọn dẹp
        } finally {
            speechStreams.delete(streamId); // Xóa khỏi map
            this.logger.info(`Speech stream data deleted from map for stream: ${streamId}`);
        }
        return { success: true };
      }
    }
  },

  methods: {
    /**
     * Tạo và đảm bảo thư mục lưu trữ tồn tại
     */
    ensureStorageDir() {
      if (!fs.existsSync(storageDir)) {
        fs.mkdirSync(storageDir, { recursive: true });
      }
    },

    /**
     * Text-to-Speech sử dụng OpenAI API
     */
    async openaiTTS(text, pVoice, pSpeed, pFormat, pModel, pInstructions) {
      try {
        const globalSettings = await this.broker.call("settings.findOne");
        // Ưu tiên biến môi trường nếu có, sau đó là settings từ DB
        const apiKey = globalSettings && globalSettings.apiKeyOpenAI

        if (!apiKey) {
          throw new MoleculerClientError(i18next.t("tts.missingOpenAIKey", "OpenAI API key is not configured. Please set OPENAI_API_KEY in environment or settings."), 500, "MISSING_OPENAI_KEY");
        }

        const openai = new OpenAI({ apiKey });

        // Logic ưu tiên: Parameter > Global Setting (OpenAI specific) > Service Setting (OpenAI specific, nếu có) > Global Setting (generic) > Service Setting (generic) > Hardcoded fallback
        const model = pModel ||
                      (globalSettings && globalSettings.tts && globalSettings.tts.openai_model) ||
                      (this.settings.tts && this.settings.tts.openai_model) || // Nếu service settings có openai_model
                      "gpt-4o-mini-tts"; // Hardcoded fallback (model TTS chuẩn của OpenAI)

        const voice = pVoice ||
                      (globalSettings && globalSettings.tts && globalSettings.tts.openai_voice) ||
                      (this.settings.tts && this.settings.tts.openai_voice) || // Service OpenAI specific voice
                      (globalSettings && globalSettings.tts && globalSettings.tts.voice) || // Global generic voice
                      (this.settings.tts && this.settings.tts.voice) || // Service generic voice (từ settings block của service)
                      "alloy"; // Hardcoded fallback voice

        const speed = pSpeed ||
                      (globalSettings && globalSettings.tts && globalSettings.tts.openai_speed) ||
                      (this.settings.tts && this.settings.tts.openai_speed) || // Service OpenAI specific speed
                      (globalSettings && globalSettings.tts && globalSettings.tts.speed) || // Global generic speed
                      (this.settings.tts && this.settings.tts.speed) || // Service generic speed
                      1.0; // Hardcoded fallback speed

        const format = pFormat || "pcm";

        const requestPayload = {
          model: model,
          input: text,
          voice: voice,
          response_format: format,
          speed: speed,
        };

        if (pInstructions) {
          // OpenAI TTS API hiện tại không hỗ trợ trường instructions chính thức
          // nhưng chúng ta vẫn thêm vào để tương thích với các phiên bản API trong tương lai
          requestPayload.instructions = pInstructions;
        }

        // console.log(`Calling OpenAI TTS directly with: ${JSON.stringify(requestPayload, null, 2)}`);

        const response = await openai.audio.speech.create(requestPayload);

        // response là một Response object (tuân theo Fetch API).
        // Cần lấy ArrayBuffer rồi chuyển sang Buffer của Node.js
        const audioArrayBuffer = await response.arrayBuffer();
        const audioBuffer = Buffer.from(audioArrayBuffer);

        return audioBuffer;

      } catch (error) {
        this.logger.error("Lỗi khi gọi OpenAI TTS trực tiếp:", error.message);
        let errorMessage = i18next.t("tts.openaiError", "Không thể tạo giọng nói từ OpenAI.");
        let statusCode = 500;
        let errorCode = "OPENAI_TTS_ERROR";

        if (error instanceof OpenAI.APIError) { // Lỗi từ thư viện OpenAI (v4+)
            statusCode = error.status || 500;
            errorMessage = `OpenAI API Error (${statusCode}): ${error.message || "Unknown API Error"}`;
            if (error.code) {
                errorCode = `OPENAI_API_${String(error.code).toUpperCase()}`;
            }
            // Log thêm chi tiết lỗi nếu có
            if (error.error && error.error.message) {
                 this.logger.error(`OpenAI API detailed error: ${error.error.message}`, error.error);
                 errorMessage = `OpenAI API Error (${statusCode}): ${error.error.message}`; // Ghi đè với thông điệp cụ thể hơn
            } else if (error.message) {
                 // errorMessage đã được gán từ error.message rồi
            }
        } else if (error.response) { // Dành cho trường hợp thư viện ngầm dùng fetch/axios và trả về lỗi có response
            statusCode = error.response.status;
            if (error.response.data && error.response.data.error && error.response.data.error.message) {
                errorMessage = `OpenAI API Error (${statusCode}): ${error.response.data.error.message}`;
            } else {
                errorMessage = `OpenAI API Error (${statusCode}): ${error.response.statusText || "Unknown API response error"}`;
            }
        } else if (error.message) { // Lỗi chung khác
            errorMessage = error.message;
        }
        this.logger.error(`Detailed OpenAI Error: Status ${statusCode}, Message: "${errorMessage}", Code: ${errorCode}`, { originalErrorStack: error.stack });
        throw new MoleculerClientError(errorMessage, statusCode, errorCode, { originalErrorMessage: error.message });
      }
    },

    /**
     * Text-to-Speech sử dụng Microsoft Speech SDK
     */
    async microsoftTTS(text, voice, speed, format) {
      try {
        // Lấy thông tin cấu hình từ settings service
        const { speechKey, serviceRegion } = await this.broker.call("settings.findOne");

        if (!speechKey || !serviceRegion) {
          throw new Error("Thiếu cấu hình Microsoft Speech SDK");
        }
        console.log("voice",voice);
        return new Promise((resolve, reject) => {
          // Tạo speech config
          const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, serviceRegion);
          speechConfig.speechSynthesisVoiceName = voice || "vi-VN-HoaiMyNeural";

          // Tạo audio config và synthesizer
          const outputFilePath = path.join(storageDir, `tts_${Date.now()}.wav`);
          const audioConfig = sdk.AudioConfig.fromAudioFileOutput(outputFilePath);
          const synthesizer = new sdk.SpeechSynthesizer(speechConfig, audioConfig);

          // Thực hiện synthesis
          synthesizer.speakTextAsync(
            text,
            result => {
              synthesizer.close();
              if (result.reason === sdk.ResultReason.SynthesizingAudioCompleted) {
                // Chuyển đổi WAV sang MP3 nếu yêu cầu
                if (format.toLowerCase() === "mp3") {
                  const mp3Path = outputFilePath.replace(".wav", ".mp3");
                  ffmpeg(outputFilePath)
                    .toFormat("mp3")
                    .on("end", () => {
                      const buffer = fs.readFileSync(mp3Path);
                      // Xóa các tệp tạm
                      fs.unlinkSync(outputFilePath);
                      fs.unlinkSync(mp3Path);
                      resolve(buffer);
                    })
                    .on("error", err => reject(err))
                    .save(mp3Path);
                } else {
                  const buffer = fs.readFileSync(outputFilePath);
                  fs.unlinkSync(outputFilePath);
                  resolve(buffer);
                }
              } else {
                reject(new Error(`Lỗi tổng hợp giọng nói: ${result.reason}`));
              }
            },
            error => {
              synthesizer.close();
              reject(error);
            }
          );
        });
      } catch (error) {
        this.logger.error("Lỗi khi sử dụng Microsoft TTS:", error);
        throw new MoleculerClientError("Không thể tạo giọng nói từ Microsoft", 500);
      }
    },

    /**
     * Text-to-Speech sử dụng Google Gemini API
     */
    async googleGeminiTTS(text, voice, speed, format) {
      this.ensureStorageDir(); // Đảm bảo thư mục storage tồn tại
      try {
        const {
          geminiApiKey = "AIzaSyCYvJlBPDUahBXB9y3LwirHGTT1ZN76VW0", // Consider moving to a more secure config
          geminiTtsModel = "gemini-2.5-flash-preview-tts", // Updated model, or keep gemini-2.5-flash-preview-tts if preferred
          geminiDefaultVoice = "Zephyr"
        } = await this.broker.call("settings.findOne");

        if (!geminiApiKey) {
          throw new Error("Thiếu cấu hình Gemini API Key trong settings.");
        }

        if (!LoadedGoogleGenAIClass) {
           throw new MoleculerClientError("Google Gemini SDK (@google/genai) is not available or not loaded. Please check installation and server logs.", 500, "GEMINI_SDK_UNAVAILABLE");
        }

        const client = new LoadedGoogleGenAIClass({apiKey : geminiApiKey});

        const supportedVoices = [
          'Zephyr', 'Puck', 'Charon', 'Kore', 'Fenrir', 'Leda', 'Orus', 'Aoede', 'Callirhoe',
          'Autonoe', 'Enceladus', 'Iapetus', 'Umbriel', 'Algieba', 'Despina', 'Erinome',
          'Algenib', 'Rasalgethi', 'Laomedeia', 'Achernar', 'Alnilam', 'Schedar', 'Gacrux',
          'Pulcherrima', 'Achird', 'Zubenelgenubi', 'Vindemiatrix', 'Sadachbia',
          'Sadaltager', 'Sulafar'
        ];
        const selectedVoice = voice && supportedVoices.includes(voice) ? voice : geminiDefaultVoice;

        const textToSynthesize = text;

        this.logger.info(`Gọi Google Gemini TTS với model: ${geminiTtsModel}, voice: ${selectedVoice}`);

        // Cấu trúc gọi API với streaming
        const streamResponse = await client.models.generateContentStream({
          model: geminiTtsModel,
          contents: [{ parts: [{ text: textToSynthesize }] }],
          config: {
            responseModalities: ['AUDIO'],
            speechConfig: {
              voiceConfig: {
                prebuiltVoiceConfig: { voiceName: selectedVoice },
              },
            },
          },
          safetySettings: [
            { category: 'HARM_CATEGORY_HARASSMENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
            { category: 'HARM_CATEGORY_HATE_SPEECH', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
            { category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
            { category: 'HARM_CATEGORY_DANGEROUS_CONTENT', threshold: 'BLOCK_MEDIUM_AND_ABOVE' },
          ],
        });

        // Trả về stream và xử lý data ngay khi nhận
        if (format.toLowerCase() === 'mp3') {
          const pcmInput = new PassThrough();
          const mp3Stream = new PassThrough();
          ffmpeg(pcmInput)
            .inputFormat('s16le')
            .audioChannels(1)
            .audioFrequency(24000)
            .toFormat('mp3')
            .on('error', err => {
              this.logger.error('Lỗi khi chuyển đổi PCM sang MP3 cho Gemini:', err);
              mp3Stream.destroy(err);
            })
            .pipe(mp3Stream);
          (async () => {
            try {
              for await (const chunk of streamResponse) {
                const inlineData = chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData;
                if (inlineData?.data) {
                  pcmInput.write(Buffer.from(inlineData.data, 'base64'));
                }
              }
              pcmInput.end();
            } catch (err) {
              pcmInput.destroy(err);
            }
          })();
          return mp3Stream;
        } else {
          const wavWriter = new wav.Writer({ sampleRate: 24000, channels: 1, bitDepth: 16 });
          (async () => {
            try {
              for await (const chunk of streamResponse) {
                const inlineData = chunk.candidates?.[0]?.content?.parts?.[0]?.inlineData;
                if (inlineData?.data) {
                  wavWriter.write(Buffer.from(inlineData.data, 'base64'));
                }
              }
              wavWriter.end();
            } catch (err) {
              wavWriter.destroy(err);
            }
          })();
          return wavWriter;
        }

      } catch (error) {
        this.logger.error("Lỗi khi sử dụng Google Gemini TTS:", error);
        if (error instanceof MoleculerClientError) throw error;
        if (error.message && (error.message.includes("API Key") || error.message.includes("permission denied"))) {
            throw new MoleculerClientError("Lỗi cấu hình hoặc xác thực Google Gemini: " + error.message, 500, "GEMINI_AUTH_ERROR", { originalError: error.message });
        }
        throw new MoleculerClientError("Không thể tạo giọng nói từ Google Gemini.", 500, "GEMINI_TTS_ERROR", { originalError: error.message });
      }
    },

    /**
     * Speech-to-Text sử dụng OpenAI Whisper API
     */
    async openaiSTT(audioBuffer, audioPath, language) {
      let finalAudioPath = audioPath;
      let tempFileCreated = false;
      try {
        this.logger.info(`openaiSTT: Bắt đầu xử lý. Language: ${language}. audioPath: ${audioPath}, audioBuffer exists: ${!!audioBuffer}`);

        // Nếu cung cấp buffer, lưu vào tệp tạm thời
        if (audioBuffer && !finalAudioPath) {
          const tempFileName = `stt_openai_${uuidv4()}.wav`; // Sử dụng uuid để tránh trùng lặp
          finalAudioPath = path.join(storageDir, tempFileName);
          this.logger.info(`openaiSTT: Tạo file tạm từ buffer: ${finalAudioPath}`);
          // Lưu ý: Việc ghi buffer trực tiếp thành file .wav giả định buffer này đã là một file WAV hợp lệ.
          // Nếu audioBuffer là raw PCM, cần thêm WAV header.
          fs.writeFileSync(finalAudioPath, audioBuffer);
          tempFileCreated = true;
        }

        if (!finalAudioPath) {
          this.logger.error("openaiSTT: Không có đường dẫn âm thanh hợp lệ.");
          throw new MoleculerClientError("Không có đường dẫn âm thanh hợp lệ để xử lý.", 400);
        }

        this.logger.info(`openaiSTT: Gọi whisper.transcriptAudio với audioPath: ${finalAudioPath}`);
        // Gọi action 'transcriptAudio' của service 'whisper'
        const result = await this.broker.call("whisper.transcriptAudio", {
          audioPath: finalAudioPath,
          // model: "whisper-1" // Model được xử lý trong whisper.service, action này không trả về.
        });
        this.logger.info("openaiSTT: Kết quả từ whisper.transcriptAudio:", result);

        // Xóa tệp tạm nếu được tạo từ buffer
        if (tempFileCreated && fs.existsSync(finalAudioPath)) {
          this.logger.info(`openaiSTT: Xóa file tạm: ${finalAudioPath}`);
          fs.unlinkSync(finalAudioPath);
        }

        // Xử lý kết quả từ whisper.transcriptAudio
        if (result && typeof result.text === 'string') { // API thành công sẽ trả về object có trường text
          return {
            text: result.text,
            language: language // Giữ lại ngôn ngữ đầu vào. Whisper tự phát hiện nhưng action này không trả về.
          };
        } else if (result && result.error) { // Trường hợp whisper.service trả về lỗi có cấu trúc { error: "..." }
          this.logger.error(`openaiSTT: Lỗi từ whisper.transcriptAudio (có cấu trúc error): ${result.error}`);
          throw new MoleculerClientError(`Lỗi từ Whisper service: ${result.error}`, 500);
        } else if (result && result.message && result.stack) { // Trường hợp là một Error object (ít xảy ra nếu action có try-catch)
             this.logger.error(`openaiSTT: Lỗi Exception từ whisper.transcriptAudio: ${result.message}`);
             throw new MoleculerClientError(`Lỗi Exception từ Whisper service: ${result.message}`, 500);
        } else { // Các trường hợp lỗi không mong muốn khác hoặc kết quả không đúng định dạng
          this.logger.error("openaiSTT: Kết quả không mong muốn hoặc lỗi không xác định từ whisper.transcriptAudio:", result);
          throw new MoleculerClientError("Không thể nhận dạng giọng nói, kết quả không hợp lệ hoặc lỗi không xác định từ Whisper.", 500);
        }

      } catch (error) {
        this.logger.error(`openaiSTT: Exception trong quá trình xử lý: ${error.message}`, error);
        // Dọn dẹp file tạm nếu có lỗi và file đã được tạo
        if (tempFileCreated && finalAudioPath && fs.existsSync(finalAudioPath)) {
          this.logger.info(`openaiSTT: Dọn dẹp file tạm ${finalAudioPath} do lỗi.`);
          fs.unlinkSync(finalAudioPath);
        }
        if (error instanceof MoleculerClientError) throw error; // Ném lại lỗi đã biết
        // Bọc lỗi chung hơn
        throw new MoleculerClientError(`Lỗi xử lý Speech-to-Text với OpenAI: ${error.message}`, 500, "STT_PROCESSING_ERROR", { originalError: error.message });
      }
    },

    /**
     * Speech-to-Text sử dụng Microsoft Speech SDK
     */
    async microsoftSTT(audioBuffer, audioPath, language) {
      try {
        // Lấy thông tin cấu hình từ settings service
        const { speechKey, serviceRegion } = await this.broker.call("settings.findOne");

        if (!speechKey || !serviceRegion) {
          throw new Error("Thiếu cấu hình Microsoft Speech SDK");
        }

        return new Promise((resolve, reject) => {
          // Tạo speech config
          const speechConfig = sdk.SpeechConfig.fromSubscription(speechKey, serviceRegion);
          speechConfig.speechRecognitionLanguage = language || "vi-VN";

          let audioConfig;
          let tempFilePath;

          // Tạo audio config từ buffer hoặc file
          if (audioBuffer) {
            tempFilePath = path.join(storageDir, `stt_${Date.now()}.wav`);
            fs.writeFileSync(tempFilePath, audioBuffer);
            audioConfig = sdk.AudioConfig.fromWavFileInput(tempFilePath);
          } else if (audioPath) {
            audioConfig = sdk.AudioConfig.fromWavFileInput(audioPath);
          } else {
            return reject(new Error("Cần cung cấp audioBuffer hoặc audioPath"));
          }

          // Tạo recognizer
          const recognizer = new sdk.SpeechRecognizer(speechConfig, audioConfig);

          // Thực hiện recognition
          recognizer.recognizeOnceAsync(
            result => {
              recognizer.close();

              // Xóa tệp tạm nếu được tạo
              if (tempFilePath) {
                fs.unlinkSync(tempFilePath);
              }

              if (result.reason === sdk.ResultReason.RecognizedSpeech) {
                resolve({
                  text: result.text,
                  language: language
                });
              } else {
                reject(new Error(`Lỗi nhận dạng giọng nói: ${result.reason}`));
              }
            },
            error => {
              recognizer.close();
              if (tempFilePath) {
                fs.unlinkSync(tempFilePath);
              }
              reject(error);
            }
          );
        });
      } catch (error) {
        this.logger.error("Lỗi khi sử dụng Microsoft STT:", error);
        throw new MoleculerClientError("Không thể nhận dạng giọng nói bằng Microsoft", 500);
      }
    },

    /**
     * Lưu nhiều đoạn âm thanh vào một tệp WAV
     */
    async saveAudioChunksToFile(audioChunks, sessionId) {
      return new Promise((resolve, reject) => {
        try {
          const fileName = `roleplay_${sessionId}_${Date.now()}.wav`;
          const filePath = path.join(storageDir, fileName);

          // Tạo file WAV
          const fileStream = fs.createWriteStream(filePath);
          const wavWriter = new wav.Writer({
            sampleRate: 16000,
            channels: 1,
            bitDepth: 16
          });

          wavWriter.pipe(fileStream);

          // Ghi các chunk vào file
          audioChunks.forEach(chunk => {
            wavWriter.write(Buffer.from(chunk));
          });

          wavWriter.end();

          // Khi ghi xong
          fileStream.on("finish", () => {
            resolve(filePath);
          });

          fileStream.on("error", (error) => {
            reject(error);
          });
        } catch (error) {
          reject(error);
        }
      });
    },

    /**
     * Phiên mã âm thanh với dấu thời gian
     */
    async transcribeWithTimestamps(audioPath, language) {
      try {
        // Gọi Whisper API với yêu cầu dấu thời gian
        const result = await this.broker.call("whisper.segmentTranscript", {
          audioPath,
          language
        });

        return {
          text: result.text,
          segments: result.segments,
          duration: result.duration,
          language: result.language || language
        };
      } catch (error) {
        this.logger.error("Lỗi khi phiên mã với dấu thời gian:", error);
        throw error;
      }
    },
  },

  async created() {
    try {
      const genaiModule = await import('@google/genai');
      if (genaiModule && genaiModule.GoogleGenAI) {
        LoadedGoogleGenAIClass = genaiModule.GoogleGenAI;
        this.logger.info("Successfully dynamically imported @google/genai. GoogleGenAI class is loaded.");
      } else {
        this.logger.warn("@google/genai loaded, but GoogleGenAI class not found within the module.");
        LoadedGoogleGenAIClass = null;
      }
    } catch (e) {
      this.logger.warn(`Failed to dynamically import @google/genai. Google Gemini TTS provider will not be available. Error: ${e.message}`);
      LoadedGoogleGenAIClass = null;
    }
    // Khởi tạo service
  },

  async started() {
    // Đảm bảo thư mục lưu trữ tồn tại
    this.ensureStorageDir();
    if (!LoadedGoogleGenAIClass) {
        this.logger.warn("@google/genai was not loaded. Google Gemini TTS provider is non-functional.");
    }
  },

  async stopped() {
    // Dọn dẹp khi dừng service
  }
};
