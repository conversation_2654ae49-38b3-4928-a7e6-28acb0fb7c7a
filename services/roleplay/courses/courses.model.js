const mongoose = require('mongoose');
const {Schema} = require('mongoose');
const {
  ROLEPLAY_COURSES,
  FILE,
  USER,
  ORGANIZATION,
  ROLEPLAY_REFERENCES,
} = require('../../../constants/dbCollections');

const courseSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
      maxlength: 255, // Tăng giới hạn để có tên dài hơn
      trim: true,
    },
    description: {
      // Mô tả khoá học
      type: String,
      maxlength: 5000,
      trim: true,
    },
    introduction: {
      // Giới thiệu khoá học và nhiệm vụ
      type: String,
      maxlength: 5000,
      trim: true,
    },
    references: [
      {
        // Tài liệu tham khảo (video, youtube, docs, image, url, audio)
        type: mongoose.Schema.Types.ObjectId,
        ref: ROLEPLAY_REFERENCES,
      },
    ],

    organizationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: ORGANIZATION,
      // required: true, // <PERSON><PERSON> theo logic, có thể không yêu cầu nếu là course cá nhân
      index: true,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      // required: true,
    },
    isDeleted: {type: Boolean, default: false, index: true},
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
      index: true,
    },
    thumbnailId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE,
    },
  },
  {
    timestamps: {
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
    },
    versionKey: false,
  },
);

// Indexes
courseSchema.index({name: 1, organizationId: 1});

module.exports = mongoose.model(ROLEPLAY_COURSES, courseSchema, ROLEPLAY_COURSES);
