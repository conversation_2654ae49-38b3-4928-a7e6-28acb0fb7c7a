"use strict";
const CronJob = require('moleculer-cron');
const {CronTime} = require('cron');

/** @type {ServiceSchema} */
module.exports = {
  name: "cronjobs",
  mixins: [CronJob],
  dependencies: ['settings'],
  settings: {
    runOnInit: true,
    cronJobs: [
      {
        // Define your cron expression (runs at 1h every day)
        name: "clear-storage",
        cronTime: '0 1 * * *',
        async onTick() {
          try {
            console.log('Job clear audio');
            await this.broker.call('audios.clearAudio');
            console.log('Job clear image');
            await this.broker.call('images.clearImage');
            console.log('Job clear file student upload');
            await this.broker.emit('jobClearFileStudentUpload');
          } catch (error) {
            console.error('Error:', error.message);
          }
        },
      }
    ]
  },

  actions: {},
  methods: {},
  events: {
    settingUpdate: {
      async handler(ctx) {
        const data = ctx.params
        if (data.cronTime) {
          this.getJob('clear-storage').setTime(new CronTime(data.cronTime));
        }
      }
    }
  },
  /**
   * Service created lifecycle event handler
   */
  created() {
  },

  /**
   * Service started lifecycle event handler
   */
  async started() {
    try {
      const cronTime = await this.broker.call('settings.getCronTime', {}) || '0 1 * * *';
      this.getJob('clear-storage').setTime(new CronTime(cronTime));
    } catch (error) {
      console.error('Error:', error);
    }
  },

  /**
   * Service stopped lifecycle event handler
   */
  async stopped() {
  },
};
