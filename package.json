{"name": "clickee-coach-roleplay-backend", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON> Coach Roleplay Backend - Microservices for AI-powered roleplay training", "scripts": {"dev": "moleculer-runner --hot services", "start": "moleculer-runner --instances 2 services", "cli": "moleculer connect NATS", "ci": "jest --watch", "test": "jest --coverage", "lint": "eslint services", "dc:up": "docker-compose up --build -d", "dc:logs": "docker-compose logs -f", "dc:down": "docker-compose down"}, "keywords": ["microservices", "moleculer", "roleplay", "ai-training", "speech-processing"], "author": "Clickee Coach Team", "devDependencies": {"@babel/eslint-parser": "^7.21.3", "@eslint/js": "^9.17.0", "dotenv": "^16.4.5", "eslint": "^9.17.0", "globals": "^15.14.0", "jest": "^27.5.1", "jest-cli": "^27.5.1", "moleculer-repl": "^0.7.4", "prettier": "^3.0.3"}, "dependencies": {"@distube/ytdl-core": "^4.16.12", "@ffmpeg-installer/ffmpeg": "^1.1.0", "@google/genai": "^1.0.1", "axios": "^1.6.7", "bcryptjs": "^2.4.3", "carbone": "^3.5.6", "cookie": "^0.5.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "dotenv": "^16.3.1", "express": "^4.18.2", "fluent-ffmpeg": "^2.1.2", "form-data": "2.3.3", "get-audio-duration": "^4.0.1", "gm": "^1.25.1", "groq-sdk": "^0.27.0", "i18next": "^22.4.11", "i18next-fs-backend": "^2.1.1", "i18next-http-backend": "^2.1.1", "i18next-http-middleware": "^3.3.0", "i18next-node-fs-backend": "^2.1.3", "jsonwebtoken": "^9.0.0", "lodash": "4.17.21", "microsoft-cognitiveservices-speech-sdk": "^1.41.0", "mime-types": "^3.0.1", "moleculer": "^0.14.26", "moleculer-cron": "^0.0.7", "moleculer-db": "^0.8.20", "moleculer-db-adapter-mongoose": "^0.8.13", "moleculer-web": "^0.10.4", "moment": "^2.30.1", "mongoose": "^5.11.15", "mongoose-paginate-v2": "^1.7.1", "nats": "^2.7.1", "node-cron": "^3.0.3", "node-fetch": "2", "node-poppler": "^8.0.3", "node-tesseract-ocr": "^2.2.1", "nodemailer": "^7.0.5", "openai": "^4.58.2", "pdf-parse": "^1.1.1", "sharp": "^0.33.5", "sherpa-onnx-node": "^1.12.0", "socket.io": "^4.8.1", "thumbsupply": "^0.4.0", "wav": "^1.0.2", "youtube-transcript": "^1.2.1", "youtubei.js": "^15.0.1", "zod": "^3.23.8"}, "engines": {"node": ">= 16.x.x"}, "jest": {"coverageDirectory": "../coverage", "testEnvironment": "node", "rootDir": "./services", "roots": ["../test"]}}